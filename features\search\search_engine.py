#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搜索引擎模块

提供统一的搜索接口和功能。
"""

import time
import logging
import numpy as np
from typing import List, Dict, Any, Optional, Union, Tuple
from pathlib import Path

from .search_request import SearchRequest
from .search_response import SearchResponse, SearchResponseBuilder
from .similarity_search import SimilaritySearch
from features.utils.feature_utils import validate_features
from features.utils.image_utils import validate_image_path

logger = logging.getLogger(__name__)


class SearchEngine:
    """搜索引擎"""
    
    def __init__(self, 
                 feature_extractor=None,
                 fabric_repository=None,
                 use_gpu: bool = True):
        """初始化搜索引擎
        
        Args:
            feature_extractor: 特征提取器
            fabric_repository: 布料数据仓库
            use_gpu: 是否使用GPU
        """
        self.feature_extractor = feature_extractor
        self.fabric_repository = fabric_repository
        self.similarity_search = SimilaritySearch(use_gpu=use_gpu)
        
        # 特征缓存
        self.feature_cache = {}
        
        logger.info("Initialized SearchEngine")
    
    def search(self, request: SearchRequest) -> SearchResponse:
        """执行搜索
        
        Args:
            request: 搜索请求
            
        Returns:
            SearchResponse: 搜索响应
        """
        builder = SearchResponseBuilder(
            query_image_path=request.query_image_path or str(request.query_id)
        )
        
        try:
            # 验证请求
            if not request.validate():
                return builder.set_error("Invalid search request").build()
            
            # 获取查询特征
            query_features = self._get_query_features(request, builder)
            if query_features is None:
                return builder.build()
            
            # 获取候选特征
            candidates = self._get_candidate_features(request)
            if not candidates:
                return builder.set_error("No candidate features found").build()
            
            builder.set_total_candidates(len(candidates))
            
            # 执行相似度搜索
            similarity_start_time = time.time()
            
            similarities = self._compute_similarities(
                query_features, candidates, request
            )
            
            similarity_time = time.time() - similarity_start_time
            builder.set_similarity_computation_time(similarity_time)
            
            # 构建搜索结果
            results = self._build_search_results(
                similarities, candidates, request
            )
            
            builder.add_results(results)
            
            return builder.build()
            
        except Exception as e:
            logger.error(f"Error in search: {str(e)}")
            return builder.set_error(str(e)).build()
    
    def _get_query_features(self, 
                           request: SearchRequest,
                           builder: SearchResponseBuilder) -> Optional[np.ndarray]:
        """获取查询特征
        
        Args:
            request: 搜索请求
            builder: 响应构建器
            
        Returns:
            Optional[np.ndarray]: 查询特征向量
        """
        try:
            # 验证输入参数
            if not request:
                builder.set_error("搜索请求为空")
                return None
            
            # 如果直接提供了特征向量
            if request.query_features is not None:
                # 验证特征向量
                if not isinstance(request.query_features, np.ndarray):
                    try:
                        features = np.array(request.query_features, dtype=np.float32)
                    except Exception as e:
                        builder.set_error(f"无法转换查询特征为数组: {e}")
                        return None
                else:
                    features = request.query_features.astype(np.float32)
                
                # 检查特征维度
                if features.size == 0:
                    builder.set_error("查询特征为空")
                    return None
                
                # 检查异常值
                if np.isnan(features).any() or np.isinf(features).any():
                    self.logger.warning("查询特征包含异常值，进行清理")
                    features = np.nan_to_num(features, nan=0.0, posinf=1.0, neginf=-1.0)
                
                if validate_features(features):
                    return features
                else:
                    builder.set_error("查询特征验证失败")
                    return None
            
            # 如果提供了查询ID
            if request.query_id is not None:
                try:
                    features = self._get_features_by_id(request.query_id)
                    if features is not None:
                        # 验证获取的特征
                        if not isinstance(features, np.ndarray):
                            features = np.array(features, dtype=np.float32)
                        
                        # 检查异常值
                        if np.isnan(features).any() or np.isinf(features).any():
                            self.logger.warning(f"ID {request.query_id} 的特征包含异常值，进行清理")
                            features = np.nan_to_num(features, nan=0.0, posinf=1.0, neginf=-1.0)
                        
                        return features
                    else:
                        builder.set_error(f"未找到ID为 {request.query_id} 的特征")
                        return None
                except Exception as e:
                    builder.set_error(f"获取ID {request.query_id} 的特征时出错: {e}")
                    return None
            
            # 如果提供了图像路径
            if request.query_image_path is not None:
                try:
                    # 验证图像路径
                    if not validate_image_path(request.query_image_path):
                        builder.set_error(f"无效的查询图像路径: {request.query_image_path}")
                        return None
                    
                    # 检查文件是否存在
                    from pathlib import Path
                    image_path = Path(request.query_image_path)
                    if not image_path.exists():
                        builder.set_error(f"查询图像文件不存在: {request.query_image_path}")
                        return None
                    
                    if not image_path.is_file():
                        builder.set_error(f"查询图像路径不是文件: {request.query_image_path}")
                        return None
                    
                    # 提取特征
                    feature_start_time = time.time()
                    features = self._extract_features_from_image(
                        request.query_image_path, request
                    )
                    feature_time = time.time() - feature_start_time
                    builder.set_feature_extraction_time(feature_time)
                    
                    if features is not None:
                        # 验证提取的特征
                        if not isinstance(features, np.ndarray):
                            features = np.array(features, dtype=np.float32)
                        
                        # 检查异常值
                        if np.isnan(features).any() or np.isinf(features).any():
                            self.logger.warning(f"从图像 {request.query_image_path} 提取的特征包含异常值，进行清理")
                            features = np.nan_to_num(features, nan=0.0, posinf=1.0, neginf=-1.0)
                        
                        return features
                    else:
                        builder.set_error(f"从图像 {request.query_image_path} 提取特征失败")
                        return None
                        
                except Exception as e:
                    builder.set_error(f"处理查询图像时出错: {e}")
                    return None
            
            builder.set_error("未提供有效的查询条件（特征向量、ID或图像路径）")
            return None
            
        except Exception as e:
            logger.error(f"获取查询特征时发生未知错误: {str(e)}")
            builder.set_error(f"获取查询特征时发生错误: {str(e)}")
            return None
    
    def _get_features_by_id(self, item_id: Union[int, str]) -> Optional[np.ndarray]:
        """根据ID获取特征
        
        Args:
            item_id: 项目ID
            
        Returns:
            Optional[np.ndarray]: 特征向量
        """
        try:
            # 验证输入参数
            if item_id is None:
                logger.warning("项目ID为空")
                return None
            
            # 检查缓存
            if item_id in self.feature_cache:
                cached_features = self.feature_cache[item_id]
                # 验证缓存的特征
                if isinstance(cached_features, np.ndarray) and cached_features.size > 0:
                    return cached_features
                else:
                    # 移除无效的缓存
                    del self.feature_cache[item_id]
            
            # 从数据库获取
            if self.fabric_repository is not None:
                try:
                    fabric_image = self.fabric_repository.get_by_id(item_id)
                    if fabric_image and fabric_image.features:
                        try:
                            import pickle
                            features = pickle.loads(fabric_image.features)
                            
                            # 验证特征
                            if isinstance(features, np.ndarray) and features.size > 0:
                                # 检查异常值
                                if np.isnan(features).any() or np.isinf(features).any():
                                    logger.warning(f"ID {item_id} 的特征包含异常值，进行清理")
                                    features = np.nan_to_num(features, nan=0.0, posinf=1.0, neginf=-1.0)
                                
                                # 缓存特征
                                self.feature_cache[item_id] = features
                                return features
                            else:
                                logger.warning(f"ID {item_id} 的特征无效")
                                return None
                                
                        except (pickle.PickleError, ValueError, TypeError) as e:
                            logger.error(f"反序列化ID {item_id} 的特征失败: {e}")
                            return None
                    else:
                        logger.debug(f"ID {item_id} 没有特征数据")
                        return None
                        
                except Exception as e:
                    logger.error(f"从数据库获取ID {item_id} 的特征失败: {e}")
                    return None
            else:
                logger.warning("布料数据仓库不可用")
                return None
            
        except Exception as e:
            logger.error(f"获取ID {item_id} 的特征时发生未知错误: {str(e)}")
            return None
    
    def _extract_features_from_image(self, 
                                   image_path: str,
                                   request: SearchRequest) -> Optional[np.ndarray]:
        """从图像提取特征
        
        Args:
            image_path: 图像路径
            request: 搜索请求
            
        Returns:
            Optional[np.ndarray]: 特征向量
        """
        try:
            # 验证输入参数
            if not image_path:
                logger.error("图像路径为空")
                return None
            
            if self.feature_extractor is None:
                logger.error("特征提取器不可用")
                return None
            
            # 验证图像路径
            from pathlib import Path
            image_path_obj = Path(image_path)
            if not image_path_obj.exists():
                logger.error(f"图像文件不存在: {image_path}")
                return None
            
            if not image_path_obj.is_file():
                logger.error(f"图像路径不是文件: {image_path}")
                return None
            
            # 检查文件大小
            try:
                file_size = image_path_obj.stat().st_size
                if file_size == 0:
                    logger.error(f"图像文件为空: {image_path}")
                    return None
                
                if file_size > 100 * 1024 * 1024:  # 100MB限制
                    logger.error(f"图像文件过大: {image_path}, 大小: {file_size / 1024 / 1024:.1f}MB")
                    return None
            except OSError as e:
                logger.error(f"无法获取图像文件信息: {image_path}, 错误: {e}")
                return None
            
            # 提取特征
            try:
                extract_traditional = hasattr(request, 'feature_types') and 'traditional' in request.feature_types
                result = self.feature_extractor.extract_features(
                    image_path, 
                    extract_traditional=extract_traditional
                )
                
                if not result:
                    logger.error(f"特征提取返回空结果: {image_path}")
                    return None
                
                if not result.success:
                    error_msg = getattr(result, 'error_message', '未知错误')
                    logger.error(f"特征提取失败: {image_path}, 错误: {error_msg}")
                    return None
                
                if result.features is None:
                    logger.error(f"提取的特征为空: {image_path}")
                    return None
                
                # 验证特征
                features = result.features
                if not isinstance(features, np.ndarray):
                    try:
                        features = np.array(features, dtype=np.float32)
                    except Exception as e:
                        logger.error(f"无法转换特征为数组: {image_path}, 错误: {e}")
                        return None
                
                if features.size == 0:
                    logger.error(f"特征数组为空: {image_path}")
                    return None
                
                # 检查异常值
                if np.isnan(features).any() or np.isinf(features).any():
                    logger.warning(f"从图像 {image_path} 提取的特征包含异常值，进行清理")
                    features = np.nan_to_num(features, nan=0.0, posinf=1.0, neginf=-1.0)
                
                return features
                
            except Exception as e:
                logger.error(f"特征提取过程中发生异常: {image_path}, 错误: {e}")
                return None
                
        except Exception as e:
            logger.error(f"从图像提取特征时发生未知错误: {image_path}, 错误: {str(e)}")
            return None
    
    def _get_candidate_features(self, request: SearchRequest) -> List[Dict[str, Any]]:
        """获取候选特征
        
        Args:
            request: 搜索请求
            
        Returns:
            List[Dict[str, Any]]: 候选特征列表
        """
        try:
            candidates = []
            
            if self.fabric_repository is None:
                logger.error("Fabric repository not available")
                return candidates
            
            # 获取所有布料图像
            fabric_images = self.fabric_repository.get_all(include_inactive=False)
            
            for fabric_image in fabric_images:
                # 应用过滤器
                if not self._apply_filters(fabric_image, request):
                    continue
                
                # 获取特征
                features = self._get_features_by_id(fabric_image.id)
                if features is not None and validate_features(features):
                    candidates.append({
                        'id': fabric_image.id,
                        'image_path': fabric_image.file_path,
                        'features': features,
                        'metadata': {
                            'category': getattr(fabric_image, 'category', None),
                            'tags': getattr(fabric_image, 'tags', []),
                            'created_at': getattr(fabric_image, 'created_at', None)
                        }
                    })
            
            logger.info(f"Found {len(candidates)} candidate features")
            return candidates
            
        except Exception as e:
            logger.error(f"Error getting candidate features: {str(e)}")
            return []
    
    def _apply_filters(self, fabric_image, request: SearchRequest) -> bool:
        """应用过滤器
        
        Args:
            fabric_image: 布料图像对象
            request: 搜索请求
            
        Returns:
            bool: 是否通过过滤
        """
        try:
            # 类别过滤
            if request.filter_category is not None:
                if getattr(fabric_image, 'category', None) != request.filter_category:
                    return False
            
            # 标签过滤
            if request.filter_tags:
                image_tags = getattr(fabric_image, 'tags', [])
                if not any(tag in image_tags for tag in request.filter_tags):
                    return False
            
            # 自定义过滤器
            if request.filters:
                for key, value in request.filters.items():
                    if hasattr(fabric_image, key):
                        if getattr(fabric_image, key) != value:
                            return False
            
            return True
            
        except Exception as e:
            logger.warning(f"Error applying filters: {str(e)}")
            return True
    
    def _compute_similarities(self, 
                            query_features: np.ndarray,
                            candidates: List[Dict[str, Any]],
                            request: SearchRequest) -> List[Dict[str, Any]]:
        """计算相似度
        
        Args:
            query_features: 查询特征
            candidates: 候选特征列表
            request: 搜索请求
            
        Returns:
            List[Dict[str, Any]]: 包含'id'和'similarity'键的字典列表
        """
        try:
            # 如果使用FAISS且索引已构建
            if (request.use_faiss and 
                self.similarity_search.faiss_index is not None and
                len(candidates) > 0):
                
                # 使用FAISS搜索
                results = self.similarity_search.search_by_features(
                    query_features, request
                )
                return results
            
            # 使用NumPy搜索
            if not candidates:
                return []
            
            # 构建特征矩阵
            features_matrix = np.vstack([c['features'] for c in candidates])
            item_ids = [c['id'] for c in candidates]
            
            results = self.similarity_search._search_with_numpy(
                query_features, request, features_matrix, item_ids
            )
            
            return results
            
        except Exception as e:
            logger.error(f"Error computing similarities: {str(e)}")
            return []
    
    def _build_search_results(self, 
                            similarities: List[Dict[str, Any]],
                            candidates: List[Dict[str, Any]],
                            request: SearchRequest) -> List[Dict[str, Any]]:
        """构建搜索结果
        
        Args:
            similarities: 相似度列表，包含'id'和'similarity'键的字典列表
            candidates: 候选特征列表
            request: 搜索请求
            
        Returns:
            List[Dict[str, Any]]: 搜索结果列表
        """
        try:
            # 创建候选字典以便快速查找
            candidate_dict = {c['id']: c for c in candidates}
            
            results = []
            for rank, similarity_item in enumerate(similarities, 1):
                candidate_id = similarity_item['id']
                similarity = similarity_item['similarity']
                
                if candidate_id in candidate_dict:
                    candidate = candidate_dict[candidate_id]
                    
                    # 获取特征相似度分数
                    feature_similarities = similarity_item.get('feature_similarities', {})
                    
                    # 如果没有特征相似度分数，则使用总分作为深度特征分数
                    if not feature_similarities:
                        feature_similarities = {
                            'deep': similarity,
                            'color': similarity_item.get('color_similarity', 0.0),
                            'texture': similarity_item.get('texture_similarity', 0.0),
                            'shape': similarity_item.get('shape_similarity', 0.0)
                        }
                    
                    result = {
                        'fabric_id': str(candidate_id),
                        'image_path': candidate['image_path'],
                        'similarity_score': similarity,
                        'feature_similarities': feature_similarities,  # 存储各特征相似度分数
                        'rank': rank
                    }
                    
                    # 添加元数据
                    if request.include_metadata:
                        result['metadata'] = candidate.get('metadata', {})
                    
                    results.append(result)
            
            return results
            
        except Exception as e:
            logger.error(f"Error building search results: {str(e)}")
            return []
    
    def build_index(self, 
                   index_type: str = "IVF",
                   force_rebuild: bool = False) -> bool:
        """构建搜索索引
        
        Args:
            index_type: 索引类型
            force_rebuild: 是否强制重建
            
        Returns:
            bool: 构建是否成功
        """
        try:
            # 检查是否需要重建
            if (not force_rebuild and 
                self.similarity_search.faiss_index is not None):
                logger.info("Search index already exists, skipping build")
                return True
            
            # 获取候选特征
            request = SearchRequest()  # 使用默认请求获取所有候选
            candidates = self._get_candidate_features(request)
            
            if not candidates:
                logger.warning("No candidates found for index building")
                return False
            
            # 构建特征矩阵并确保归一化
            features_list = []
            valid_item_ids = []

            for c in candidates:
                features = c['features']
                # 确保特征已归一化
                if isinstance(features, np.ndarray) and features.size > 0:
                    norm = np.linalg.norm(features)
                    if norm > 1e-8:
                        normalized_features = features / norm
                        features_list.append(normalized_features)
                        valid_item_ids.append(c['id'])
                    else:
                        logger.warning(f"跳过零向量特征: {c.get('id', 'unknown')}")
                else:
                    logger.warning(f"跳过无效特征: {c.get('id', 'unknown')}")

            if not features_list:
                logger.warning("没有有效的特征用于构建索引")
                return False

            features_matrix = np.vstack(features_list)

            # 构建索引
            success = self.similarity_search.build_index(
                features_matrix, valid_item_ids, index_type
            )
            
            if success:
                logger.info(f"Successfully built search index with {len(candidates)} items")
            else:
                logger.error("Failed to build search index")
            
            return success
            
        except Exception as e:
            logger.error(f"Error building search index: {str(e)}")
            return False
    
    def get_index_info(self) -> Dict[str, Any]:
        """获取索引信息
        
        Returns:
            Dict[str, Any]: 索引信息
        """
        return self.similarity_search.get_index_info()
    
    def clear_cache(self):
        """清空缓存"""
        self.feature_cache.clear()
        logger.info("Cleared feature cache")
    
    def clear_index(self):
        """清空索引"""
        self.similarity_search.clear_index()
        logger.info("Cleared search index")
        
    def search_by_features(self,
                          features: np.ndarray,
                          top_k: int = 50,
                          similarity_threshold: float = 0.0,
                          filters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """基于特征向量搜索
        
        Args:
            features: 查询特征向量
            top_k: 返回结果数量
            similarity_threshold: 相似度阈值
            filters: 过滤条件
            
        Returns:
            List[Dict[str, Any]]: 搜索结果列表
        """
        try:
            # 创建搜索请求
            request = SearchRequest(
                query_features=features,
                top_k=top_k,
                similarity_threshold=similarity_threshold
            )
            
            # 设置过滤器
            if filters:
                request.filters = filters
            
            # 执行搜索
            return self.search(request).results
            
        except Exception as e:
            logger.error(f"Error in search_by_features: {str(e)}")
            return []