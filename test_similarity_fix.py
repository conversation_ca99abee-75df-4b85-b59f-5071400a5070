#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
相似度计算修复测试脚本

该脚本用于测试修复后的相似度计算是否正确工作。
"""

import os
import sys
import numpy as np
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from features.utils.feature_utils import compute_similarity, compute_batch_similarity, normalize_features
from features.search.similarity_search import SimilaritySearch, SimilarityMetric
from features.search.search_request import SearchRequest

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_feature_normalization():
    """测试特征归一化"""
    print("=" * 60)
    print("测试特征归一化")
    print("=" * 60)
    
    # 创建测试特征
    features = np.array([3.0, 4.0, 0.0])  # 范数为5
    print(f"原始特征: {features}")
    print(f"原始范数: {np.linalg.norm(features):.6f}")
    
    # L2归一化
    normalized = normalize_features(features, "l2")
    print(f"L2归一化后: {normalized}")
    print(f"归一化后范数: {np.linalg.norm(normalized):.6f}")
    
    # 验证归一化是否正确
    expected_norm = 1.0
    actual_norm = np.linalg.norm(normalized)
    assert abs(actual_norm - expected_norm) < 1e-6, f"归一化失败: 期望范数{expected_norm}, 实际范数{actual_norm}"
    print("✓ 特征归一化测试通过")


def test_similarity_calculation():
    """测试相似度计算"""
    print("\n" + "=" * 60)
    print("测试相似度计算")
    print("=" * 60)
    
    # 创建测试特征
    feature1 = np.array([1.0, 0.0, 0.0])  # 单位向量
    feature2 = np.array([1.0, 0.0, 0.0])  # 相同的单位向量
    feature3 = np.array([0.0, 1.0, 0.0])  # 垂直的单位向量
    feature4 = np.array([0.5, 0.5, 0.0])  # 45度角的向量
    
    print(f"特征1: {feature1}")
    print(f"特征2: {feature2}")
    print(f"特征3: {feature3}")
    print(f"特征4: {feature4}")
    
    # 测试余弦相似度
    sim_12 = compute_similarity(feature1, feature2, "cosine")
    sim_13 = compute_similarity(feature1, feature3, "cosine")
    sim_14 = compute_similarity(feature1, feature4, "cosine")
    
    print(f"\n余弦相似度:")
    print(f"特征1 vs 特征2 (相同): {sim_12:.6f}")
    print(f"特征1 vs 特征3 (垂直): {sim_13:.6f}")
    print(f"特征1 vs 特征4 (45度): {sim_14:.6f}")
    
    # 验证结果
    assert abs(sim_12 - 1.0) < 1e-6, f"相同向量相似度应为1.0, 实际为{sim_12}"
    assert abs(sim_13 - 0.0) < 1e-6, f"垂直向量相似度应为0.0, 实际为{sim_13}"
    assert abs(sim_14 - 0.7071067811865476) < 1e-6, f"45度向量相似度应为√2/2, 实际为{sim_14}"
    
    print("✓ 相似度计算测试通过")


def test_batch_similarity():
    """测试批量相似度计算"""
    print("\n" + "=" * 60)
    print("测试批量相似度计算")
    print("=" * 60)
    
    # 创建查询特征
    query = np.array([1.0, 0.0, 0.0]).reshape(1, -1)
    
    # 创建特征矩阵
    features_matrix = np.array([
        [1.0, 0.0, 0.0],  # 相同
        [0.0, 1.0, 0.0],  # 垂直
        [0.5, 0.5, 0.0],  # 45度
        [-1.0, 0.0, 0.0]  # 相反
    ])
    
    print(f"查询特征: {query.flatten()}")
    print(f"特征矩阵:\n{features_matrix}")
    
    # 计算批量相似度
    similarities = compute_batch_similarity(query, features_matrix, "cosine")
    print(f"\n批量相似度结果: {similarities}")
    print(f"结果形状: {similarities.shape}")
    
    # 验证结果
    expected = np.array([[1.0, 0.0, 0.7071067811865476, -1.0]])
    assert similarities.shape == expected.shape, f"结果形状错误: 期望{expected.shape}, 实际{similarities.shape}"
    
    for i, (actual, exp) in enumerate(zip(similarities[0], expected[0])):
        assert abs(actual - exp) < 1e-6, f"第{i}个相似度错误: 期望{exp}, 实际{actual}"
    
    print("✓ 批量相似度计算测试通过")


def test_faiss_similarity_search():
    """测试FAISS相似度搜索"""
    print("\n" + "=" * 60)
    print("测试FAISS相似度搜索")
    print("=" * 60)
    
    try:
        # 创建相似度搜索器
        similarity_search = SimilaritySearch()
        
        # 创建测试数据
        features_matrix = np.array([
            [1.0, 0.0, 0.0],  # ID: 0
            [0.0, 1.0, 0.0],  # ID: 1
            [0.5, 0.5, 0.0],  # ID: 2
            [0.8, 0.6, 0.0],  # ID: 3
        ], dtype=np.float32)
        
        item_ids = ['item_0', 'item_1', 'item_2', 'item_3']
        
        print(f"构建索引，特征矩阵形状: {features_matrix.shape}")
        print(f"项目ID: {item_ids}")
        
        # 构建索引
        success = similarity_search.build_index(features_matrix, item_ids, "Flat")
        assert success, "索引构建失败"
        print("✓ 索引构建成功")
        
        # 创建搜索请求
        query_features = np.array([1.0, 0.0, 0.0], dtype=np.float32)
        request = SearchRequest(
            query_features=query_features,
            top_k=4,
            similarity_metric=SimilarityMetric.COSINE,
            similarity_threshold=0.0
        )
        
        print(f"查询特征: {query_features}")
        
        # 执行搜索
        results = similarity_search.search_by_features(query_features, request)
        
        print(f"\n搜索结果:")
        for i, result in enumerate(results):
            print(f"  {i+1}. ID: {result['id']}, 相似度: {result['similarity']:.6f}")
        
        # 验证结果
        assert len(results) > 0, "搜索结果为空"
        assert results[0]['id'] == 'item_0', f"最相似的应该是item_0, 实际是{results[0]['id']}"
        assert abs(results[0]['similarity'] - 1.0) < 1e-6, f"自相似度应该是1.0, 实际是{results[0]['similarity']}"
        
        print("✓ FAISS相似度搜索测试通过")
        
    except ImportError:
        print("⚠ FAISS未安装，跳过FAISS测试")
    except Exception as e:
        print(f"✗ FAISS测试失败: {e}")
        raise


def test_edge_cases():
    """测试边界情况"""
    print("\n" + "=" * 60)
    print("测试边界情况")
    print("=" * 60)
    
    # 测试零向量
    zero_vector = np.array([0.0, 0.0, 0.0])
    unit_vector = np.array([1.0, 0.0, 0.0])
    
    print("测试零向量处理...")
    sim = compute_similarity(zero_vector, unit_vector, "cosine")
    print(f"零向量与单位向量的相似度: {sim}")
    
    # 测试非常小的向量
    tiny_vector = np.array([1e-10, 1e-10, 1e-10])
    sim_tiny = compute_similarity(tiny_vector, unit_vector, "cosine")
    print(f"微小向量与单位向量的相似度: {sim_tiny}")
    
    # 测试归一化后的一致性
    large_vector = np.array([1000.0, 2000.0, 3000.0])
    small_vector = np.array([0.001, 0.002, 0.003])
    
    # 这两个向量方向相同，归一化后应该完全相同
    sim_direction = compute_similarity(large_vector, small_vector, "cosine")
    print(f"同方向不同大小向量的相似度: {sim_direction:.6f}")
    assert abs(sim_direction - 1.0) < 1e-6, f"同方向向量相似度应为1.0, 实际为{sim_direction}"
    
    print("✓ 边界情况测试通过")


def main():
    """主测试函数"""
    print("开始相似度计算修复测试...")
    
    try:
        test_feature_normalization()
        test_similarity_calculation()
        test_batch_similarity()
        test_faiss_similarity_search()
        test_edge_cases()
        
        print("\n" + "=" * 60)
        print("🎉 所有测试通过！相似度计算修复成功！")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == '__main__':
    sys.exit(main())
